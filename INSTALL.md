# DropKnife 插件安装指南

## 📋 前置要求

1. **CS2 服务器** - 确保你有一个运行中的CS2服务器
2. **CounterStrikeSharp** - 服务器必须已安装CounterStrikeSharp框架
   - 下载地址: https://github.com/roflmuffin/CounterStrikeSharp
   - 最低版本要求: API 210+

## 🚀 安装步骤

### 1. 编译插件

在项目目录下运行：
```bash
dotnet build --configuration Release
```

或者直接运行 `build.bat` 文件。

### 2. 复制文件

将以下文件复制到服务器的插件目录：

**源文件位置：**
- `bin/Release/net8.0/DropKnife.dll`
- `config.json` (可选，插件会自动创建)

**目标位置：**
```
addons/counterstrikesharp/plugins/DropKnife/
├── DropKnife.dll
└── config.json
```

### 3. 重启服务器

重启CS2服务器或使用以下命令重载插件：
```
css_plugins reload
```

## ✅ 验证安装

1. 在服务器控制台查看是否有加载成功的消息：
   ```
   [DropKnife] Plugin loaded successfully!
   ```

2. 在游戏中测试命令：
   - 打开控制台输入: `css_drop` 或 `drop`
   - 应该会在脚下生成匕首

## ⚙️ 配置文件

插件首次运行时会自动创建 `config.json` 文件：

```json
{
  "knife_count": 5,
  "spawn_radius": 50.0,
  "spawn_height": 10.0,
  "command_cooldown": 5,
  "show_public_message": true,
  "allowed_teams": [2, 3],
  "minimum_players": 1
}
```

修改配置后需要重载插件：
```
css_plugins reload
```

## 🎮 使用方法

玩家可以通过以下命令使用：

**控制台命令：**
- `css_drop` - 在脚下生成匕首
- `drop` - 在脚下生成匕首
- `!drop` - 在脚下生成匕首
- `.drop` - 在脚下生成匕首

**聊天命令：**
- 在聊天框输入 `!drop` 或 `.drop`

## 🔧 故障排除

### 插件未加载
- 检查CounterStrikeSharp是否正确安装
- 确认插件文件路径正确
- 查看服务器控制台错误信息

### 命令无效果
- 检查玩家是否在允许的队伍中
- 确认是否满足最少玩家数量要求
- 检查是否在冷却时间内

### 配置不生效
- 确认config.json格式正确
- 重载插件使配置生效

## 📞 支持

如果遇到问题，请检查：
1. CounterStrikeSharp版本是否兼容
2. 服务器控制台的错误信息
3. 配置文件格式是否正确
