# 更新日志

## v1.3.0 - 2025-01-XX

### 🔄 新功能
- **双模式系统** - 新增冷却模式和每回合一次模式切换
- **模式切换命令** - 添加 `css_dropknife_mode` 命令实时切换模式
- **严格触发控制** - 确保只能通过指令和聊天触发，防止丢弃键等意外触发

### ⚙️ 配置改进
- 新增 `use_cooldown_mode` 配置项控制模式
- 新增 `once_per_round` 配置项控制每回合限制
- 优化配置文件结构和说明

### 🛡️ 安全改进
- 严格的聊天消息匹配，防止误触发
- 添加玩家存活状态检查
- 改进错误处理和日志记录

### 🔧 技术优化
- 重构模式检查逻辑
- 优化玩家验证流程
- 改进命令处理机制

## v1.2.0 - 2025-01-XX

### 🔄 重大重构
- **完全重写插件** - 基于稳定的参考实现重新编写
- **修复所有崩溃问题** - 使用安全的GiveNamedItem方法
- **简化实现逻辑** - 移除复杂的皮肤复制功能，专注于稳定性

### ✅ 新功能
- **聊天命令支持** - 支持 `!drop`, `.drop`, `.d` 聊天命令
- **EventPlayerChat处理** - 正确的聊天事件处理
- **圆形生成模式** - 匕首以圆形排列在玩家脚下

### 🛡️ 稳定性改进
- 使用经过验证的API调用
- 移除了导致崩溃的SetStateChanged操作
- 简化了武器生成逻辑
- 添加了完善的错误处理

### 🔧 技术改进
- 重新整理了代码结构
- 优化了配置系统兼容性
- 改进了玩家验证逻辑

## v1.1.0 - 2025-01-XX

### 🎨 新功能
- **皮肤继承系统** - 生成的匕首现在会完全继承玩家当前装备匕首的皮肤属性
  - 复制皮肤类型 (Paint Kit)
  - 复制磨损度 (Wear)
  - 复制图案种子 (Seed)
  - 保持匕首类型一致

### 💬 聊天命令支持
- 新增聊天命令支持：`!drop` 和 `.drop`
- 玩家现在可以在聊天框中输入命令，无需打开控制台

### 🔧 技术改进
- 重构了匕首检测系统
- 添加了 `KnifeInfo` 类来存储匕首属性
- 改进了错误处理机制

## v1.0.0 - 2025-01-XX

### 🎉 初始版本
- 基础的匕首生成功能
- 控制台命令支持：`css_drop` 和 `drop`
- 冷却时间系统
- 队伍限制功能
- 最少玩家数量限制
- 完全可配置的设置
- 中文消息支持
- 支持所有匕首类型检测

### ⚙️ 配置选项
- 可配置生成数量
- 可配置生成半径和高度
- 可配置冷却时间
- 可配置队伍限制
- 可配置公共消息显示
