# 更新日志

## v1.1.1 - 2025-01-XX

### 🐛 修复
- **修复服务器崩溃问题** - 移除了导致服务器崩溃的 `SetStateChanged` 调用
- **优化皮肤设置** - 改为在武器生成前设置皮肤属性，避免网络同步错误
- **提高稳定性** - 简化了皮肤复制逻辑，确保服务器稳定运行

### 🔧 技术改进
- 移除了 `CopyKnifeSkin` 函数，直接在武器创建时设置属性
- 添加了皮肤检查，只有当 PaintKit > 0 时才设置皮肤属性
- 优化了错误处理机制

## v1.1.0 - 2025-01-XX

### 🎨 新功能
- **皮肤继承系统** - 生成的匕首现在会完全继承玩家当前装备匕首的皮肤属性
  - 复制皮肤类型 (Paint Kit)
  - 复制磨损度 (Wear)
  - 复制图案种子 (Seed)
  - 保持匕首类型一致

### 💬 聊天命令支持
- 新增聊天命令支持：`!drop` 和 `.drop`
- 玩家现在可以在聊天框中输入命令，无需打开控制台

### 🔧 技术改进
- 重构了匕首检测系统
- 添加了 `KnifeInfo` 类来存储匕首属性
- 改进了错误处理机制

## v1.0.0 - 2025-01-XX

### 🎉 初始版本
- 基础的匕首生成功能
- 控制台命令支持：`css_drop` 和 `drop`
- 冷却时间系统
- 队伍限制功能
- 最少玩家数量限制
- 完全可配置的设置
- 中文消息支持
- 支持所有匕首类型检测

### ⚙️ 配置选项
- 可配置生成数量
- 可配置生成半径和高度
- 可配置冷却时间
- 可配置队伍限制
- 可配置公共消息显示
