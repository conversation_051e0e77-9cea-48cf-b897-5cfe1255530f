# DropKnife Plugin for CS2

一个基于CounterStrikeSharp的CS2插件，允许玩家在聊天中输入`.drop`命令在脚下生成匕首。

## 功能特性

- 🔪 在玩家脚下生成匕首（默认5把）
- 🔄 **双模式系统**：冷却模式 vs 每回合一次模式
- ⏰ 可配置的命令冷却时间
- 💬 支持聊天命令和控制台命令
- 🛡️ 严格的触发控制，只能通过指令和聊天触发
- ⚙️ 完全可配置的设置
- 👥 队伍限制和最少玩家数量限制
- 🌐 支持中文显示
- 🛡️ 稳定可靠，不会导致服务器崩溃

## 安装方法

1. 确保你的CS2服务器已安装CounterStrikeSharp
2. 将编译后的插件文件放入 `addons/counterstrikesharp/plugins/DropKnife/` 目录
3. 重启服务器或使用 `css_plugins reload` 命令

## 使用方法

玩家可以通过以下方式使用：

**控制台命令：**
```
css_drop                    # 生成匕首
drop                        # 生成匕首
css_dropknife_mode          # 切换模式（冷却模式 ↔ 每回合一次模式）
```

**聊天命令：**
玩家在聊天框中输入：
```
!drop
.drop
.d
/drop
!d
/d
```

插件会在玩家脚下以圆形排列生成匕首。

## 配置文件

插件首次运行时会在插件目录下创建 `config.json` 配置文件：

```json
{
  "knife_count": 5,
  "spawn_radius": 50.0,
  "spawn_height": 10.0,
  "command_cooldown": 5,
  "show_public_message": true,
  "allowed_teams": [2, 3],
  "minimum_players": 1
}
```

### 配置说明

- `knife_count`: 生成的匕首数量（默认：5）
- `spawn_radius`: 生成半径，单位为游戏单位（默认：50.0）
- `spawn_height`: 生成高度偏移（默认：10.0）
- `command_cooldown`: 命令冷却时间，单位为秒（默认：5）**仅在冷却模式下生效**
- `use_cooldown_mode`: 是否使用冷却模式（默认：false）true=冷却模式, false=每回合一次模式
- `once_per_round`: 每回合一次限制（默认：true）**仅在非冷却模式下生效**
- `show_public_message`: 是否向所有玩家显示使用消息（默认：true）
- `allowed_teams`: 允许使用命令的队伍 [2=恐怖分子, 3=反恐精英]（默认：[2, 3]）
- `minimum_players`: 使用命令所需的最少玩家数量（默认：1）

## 支持的匕首类型

插件支持所有CS2中的匕首类型，包括但不限于：
- 默认匕首
- 爪子刀 (Karambit)
- M9刺刀
- 折叠刀
- 猎刀
- 弯刀
- 鲍伊猎刀
- 蝴蝶刀
- 暗影双匕
- 以及其他所有匕首类型...

## 模式系统

🔄 **双模式设计**：

**模式1：冷却模式** (`use_cooldown_mode: true`)
- 玩家使用命令后需要等待指定的冷却时间
- 适合频繁使用的服务器
- 冷却时间可在配置文件中设置

**模式2：每回合一次模式** (`use_cooldown_mode: false`)
- 每个玩家每回合只能使用一次命令
- 回合结束后重置使用次数
- 适合竞技模式或限制使用的服务器

**模式切换**：
- 使用控制台命令 `css_dropknife_mode` 可以实时切换模式
- 切换时会自动清除相关的使用记录

## 安全特性

🛡️ **严格的触发控制**：
- **只能通过指令和聊天触发**，不会被丢弃键等其他操作意外触发
- 严格的消息匹配，防止误触发
- 必须是存活的玩家才能使用

🛡️ **稳定可靠**：
- 基于成熟的CounterStrikeSharp API
- 使用安全的GiveNamedItem方法生成武器
- 避免了可能导致服务器崩溃的复杂操作
- 经过测试，确保服务器稳定运行

🎯 **简单高效**：
- 直接在玩家脚下生成标准匕首
- 圆形排列，美观整齐
- 快速响应，无延迟

## 编译方法

1. 确保安装了 .NET 8.0 SDK
2. 在项目目录下运行：
```bash
dotnet build
```

## 版本要求

- CounterStrikeSharp API 版本 >= 210
- .NET 8.0

## 作者

YourName

## 许可证

MIT License
