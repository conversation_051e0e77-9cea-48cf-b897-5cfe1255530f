# DropKnife Plugin for CS2

一个基于CounterStrikeSharp的CS2插件，允许玩家在聊天中输入`.drop`命令在脚下生成匕首。

## 功能特性

- 🔪 在玩家脚下生成匕首（默认5把）
- ⏰ 命令冷却时间防止滥用
- 🎯 支持所有匕首类型（会生成玩家当前装备的匕首类型）
- 🎨 **皮肤继承** - 生成的匕首会继承玩家当前装备匕首的皮肤、磨损度等属性
- ⚙️ 完全可配置的设置
- 👥 队伍限制和最少玩家数量限制
- 🌐 支持中文显示

## 安装方法

1. 确保你的CS2服务器已安装CounterStrikeSharp
2. 将编译后的插件文件放入 `addons/counterstrikesharp/plugins/DropKnife/` 目录
3. 重启服务器或使用 `css_plugins reload` 命令

## 使用方法

玩家可以通过以下方式使用：

**控制台命令：**
```
css_drop
drop
!drop
.drop
```

**聊天命令：**
玩家在聊天框中输入：
```
!drop
.drop
```

插件会在玩家脚下以圆形排列生成匕首。

## 配置文件

插件首次运行时会在插件目录下创建 `config.json` 配置文件：

```json
{
  "knife_count": 5,
  "spawn_radius": 50.0,
  "spawn_height": 10.0,
  "command_cooldown": 5,
  "show_public_message": true,
  "allowed_teams": [2, 3],
  "minimum_players": 1
}
```

### 配置说明

- `knife_count`: 生成的匕首数量（默认：5）
- `spawn_radius`: 生成半径，单位为游戏单位（默认：50.0）
- `spawn_height`: 生成高度偏移（默认：10.0）
- `command_cooldown`: 命令冷却时间，单位为秒（默认：5）
- `show_public_message`: 是否向所有玩家显示使用消息（默认：true）
- `allowed_teams`: 允许使用命令的队伍 [2=恐怖分子, 3=反恐精英]（默认：[2, 3]）
- `minimum_players`: 使用命令所需的最少玩家数量（默认：1）

## 支持的匕首类型

插件支持所有CS2中的匕首类型，包括但不限于：
- 默认匕首
- 爪子刀 (Karambit)
- M9刺刀
- 折叠刀
- 猎刀
- 弯刀
- 鲍伊猎刀
- 蝴蝶刀
- 暗影双匕
- 以及其他所有匕首类型...

## 皮肤继承功能

🎨 **完整皮肤复制**：生成的匕首会完全继承玩家当前装备匕首的所有属性：
- **皮肤类型** - 复制相同的皮肤图案
- **磨损度** - 保持相同的磨损程度
- **图案种子** - 保持相同的图案变化
- **匕首类型** - 生成相同类型的匕首（如爪子刀、蝴蝶刀等）

这意味着如果你装备了一把崭新的爪子刀多普勒，生成的5把匕首都将是相同皮肤、相同磨损度的爪子刀多普勒！

## 编译方法

1. 确保安装了 .NET 8.0 SDK
2. 在项目目录下运行：
```bash
dotnet build
```

## 版本要求

- CounterStrikeSharp API 版本 >= 210
- .NET 8.0

## 作者

YourName

## 许可证

MIT License
