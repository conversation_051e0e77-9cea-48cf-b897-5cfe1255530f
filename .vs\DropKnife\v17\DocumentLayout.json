{"Version": 1, "WorkspaceRootPath": "E:\\.CS2 plugins\\DropKnife\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\.CS2 plugins\\DropKnife\\DropKnife.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:DropKnife.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\.CS2 plugins\\DropKnife\\DropKnife.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:DropKnife.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "DropKnife.cs", "DocumentMoniker": "E:\\.CS2 plugins\\DropKnife\\DropKnife.cs", "RelativeDocumentMoniker": "DropKnife.cs", "ToolTip": "E:\\.CS2 plugins\\DropKnife\\DropKnife.cs", "RelativeToolTip": "DropKnife.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T09:51:07.805Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "DropKnife.csproj", "DocumentMoniker": "E:\\.CS2 plugins\\DropKnife\\DropKnife.csproj", "RelativeDocumentMoniker": "DropKnife.csproj", "ToolTip": "E:\\.CS2 plugins\\DropKnife\\DropKnife.csproj", "RelativeToolTip": "DropKnife.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-29T09:51:05.94Z", "EditorCaption": ""}]}]}]}