using CounterStrikeSharp.API;
using CounterStrikeSharp.API.Core;
using CounterStrikeSharp.API.Core.Attributes;
using CounterStrikeSharp.API.Core.Attributes.Registration;
using CounterStrikeSharp.API.Modules.Commands;
using CounterStrikeSharp.API.Modules.Utils;
using System.Text.Json;

namespace DropKnife;

[MinimumApiVersion(210)]
public class DropKnifePlugin : BasePlugin
{
    public override string ModuleName => "DropKnife";
    public override string ModuleVersion => "1.0.0";
    public override string ModuleAuthor => "YourName";
    public override string ModuleDescription => "Drop knives at player's feet when typing .drop";

    private DropKnifeConfig _config = new();
    private readonly Dictionary<ulong, DateTime> _lastUsed = new();

    public override void Load(bool hotReload)
    {
        LoadConfig();
        Console.WriteLine($"[{ModuleName}] Plugin loaded successfully!");

        // 注册聊天命令监听器
        RegisterListener<Listeners.OnClientSayText>(OnClientSayText);
    }

    private void LoadConfig()
    {
        string configPath = Path.Combine(ModuleDirectory, "config.json");

        if (File.Exists(configPath))
        {
            try
            {
                string json = File.ReadAllText(configPath);
                _config = JsonSerializer.Deserialize<DropKnifeConfig>(json) ?? new DropKnifeConfig();
                Console.WriteLine($"[{ModuleName}] Config loaded successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{ModuleName}] Error loading config: {ex.Message}");
                _config = new DropKnifeConfig();
            }
        }
        else
        {
            // 创建默认配置文件
            try
            {
                string json = JsonSerializer.Serialize(_config, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(configPath, json);
                Console.WriteLine($"[{ModuleName}] Default config created at: {configPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{ModuleName}] Error creating config: {ex.Message}");
            }
        }
    }

    public override void Unload(bool hotReload)
    {
        Console.WriteLine($"[{ModuleName}] Plugin unloaded!");
    }

    private HookResult OnClientSayText(CCSPlayerController? player, CommandInfo info)
    {
        if (player == null || !player.IsValid || player.IsBot)
            return HookResult.Continue;

        string message = info.GetArg(1).Trim();

        // 检查是否输入了 .drop 命令
        if (message.Equals(".drop", StringComparison.OrdinalIgnoreCase))
        {
            // 检查冷却时间
            if (IsOnCooldown(player))
            {
                var remainingTime = GetRemainingCooldown(player);
                player.PrintToChat($" {ChatColors.Red}[DropKnife] 命令冷却中，请等待 {remainingTime} 秒!");
                return HookResult.Handled;
            }

            // 检查队伍限制
            if (!_config.AllowedTeams.Contains((int)player.Team))
            {
                player.PrintToChat($" {ChatColors.Red}[DropKnife] 你的队伍不能使用此命令!");
                return HookResult.Handled;
            }

            // 检查最少玩家数量
            if (Utilities.GetPlayers().Count < _config.MinimumPlayers)
            {
                player.PrintToChat($" {ChatColors.Red}[DropKnife] 服务器至少需要 {_config.MinimumPlayers} 名玩家才能使用此命令!");
                return HookResult.Handled;
            }

            DropKnives(player);
            return HookResult.Handled; // 阻止消息显示在聊天中
        }

        return HookResult.Continue;
    }

    private bool IsOnCooldown(CCSPlayerController player)
    {
        if (!_lastUsed.ContainsKey(player.SteamID))
            return false;

        return DateTime.Now.Subtract(_lastUsed[player.SteamID]).TotalSeconds < _config.CommandCooldown;
    }

    private int GetRemainingCooldown(CCSPlayerController player)
    {
        if (!_lastUsed.ContainsKey(player.SteamID))
            return 0;

        var elapsed = DateTime.Now.Subtract(_lastUsed[player.SteamID]).TotalSeconds;
        return Math.Max(0, (int)(_config.CommandCooldown - elapsed));
    }

    private void DropKnives(CCSPlayerController player)
    {
        if (player.PlayerPawn?.Value == null)
            return;

        var playerPawn = player.PlayerPawn.Value;
        var playerPos = playerPawn.CBodyComponent?.SceneNode?.AbsOrigin;

        if (playerPos == null)
            return;

        // 获取玩家当前装备的匕首
        string knifeWeapon = GetPlayerKnife(player);

        if (string.IsNullOrEmpty(knifeWeapon))
        {
            player.PrintToChat($" {ChatColors.Red}[DropKnife] 你没有装备匕首!");
            return;
        }

        // 记录使用时间
        _lastUsed[player.SteamID] = DateTime.Now;

        // 在玩家脚下生成匕首
        for (int i = 0; i < _config.KnifeCount; i++)
        {
            // 计算每把匕首的位置，形成一个小圆圈
            float angle = (float)(i * 2 * Math.PI / _config.KnifeCount);

            Vector spawnPos = new Vector(
                playerPos.X + (float)(Math.Cos(angle) * _config.SpawnRadius),
                playerPos.Y + (float)(Math.Sin(angle) * _config.SpawnRadius),
                playerPos.Z + _config.SpawnHeight
            );

            // 创建武器实体
            var weapon = Utilities.CreateEntityByName<CBasePlayerWeapon>("weapon_knife");
            if (weapon == null) continue;

            // 设置位置
            weapon.Teleport(spawnPos, new QAngle(0, 0, 0), new Vector(0, 0, 0));

            // 生成实体
            weapon.DispatchSpawn();
        }

        player.PrintToChat($" {ChatColors.Green}[DropKnife] 已在你脚下生成{_config.KnifeCount}把 {ChatColors.Yellow}{GetKnifeName(knifeWeapon)}{ChatColors.Green}!");

        // 向所有玩家显示消息（如果配置允许）
        if (_config.ShowPublicMessage)
        {
            Server.PrintToChatAll($" {ChatColors.LightBlue}[DropKnife] {ChatColors.White}{player.PlayerName} {ChatColors.LightBlue}使用了 .drop 命令!");
        }
    }

    private string GetPlayerKnife(CCSPlayerController player)
    {
        if (player.PlayerPawn?.Value?.WeaponServices == null)
            return string.Empty;

        var weaponServices = player.PlayerPawn.Value.WeaponServices;
        
        // 遍历玩家的武器
        foreach (var weapon in weaponServices.MyWeapons)
        {
            if (weapon?.Value?.DesignerName == null) continue;
            
            string weaponName = weapon.Value.DesignerName;
            
            // 检查是否是匕首类武器
            if (IsKnifeWeapon(weaponName))
            {
                return weaponName;
            }
        }

        return "weapon_knife"; // 默认匕首
    }

    private bool IsKnifeWeapon(string weaponName)
    {
        return weaponName.Contains("knife") || 
               weaponName.Contains("bayonet") ||
               weaponName.Contains("karambit") ||
               weaponName.Contains("m9_bayonet") ||
               weaponName.Contains("flip") ||
               weaponName.Contains("gut") ||
               weaponName.Contains("falchion") ||
               weaponName.Contains("bowie") ||
               weaponName.Contains("butterfly") ||
               weaponName.Contains("shadow_daggers") ||
               weaponName.Contains("push") ||
               weaponName.Contains("cord") ||
               weaponName.Contains("canis") ||
               weaponName.Contains("ursus") ||
               weaponName.Contains("navaja") ||
               weaponName.Contains("stiletto") ||
               weaponName.Contains("talon") ||
               weaponName.Contains("cleaver") ||
               weaponName.Contains("skeleton") ||
               weaponName.Contains("survival") ||
               weaponName.Contains("nomad") ||
               weaponName.Contains("paracord") ||
               weaponName.Contains("classic");
    }

    private string GetKnifeName(string weaponName)
    {
        return weaponName switch
        {
            "weapon_knife" => "默认匕首",
            "weapon_knife_karambit" => "爪子刀",
            "weapon_knife_m9_bayonet" => "M9刺刀",
            "weapon_knife_flip" => "折叠刀",
            "weapon_knife_gut" => "猎刀",
            "weapon_knife_falchion" => "弯刀",
            "weapon_knife_bowie" => "鲍伊猎刀",
            "weapon_knife_butterfly" => "蝴蝶刀",
            "weapon_knife_shadow_daggers" => "暗影双匕",
            "weapon_knife_push" => "推匕",
            "weapon_knife_cord" => "伞绳刀",
            "weapon_knife_canis" => "猎犬刀",
            "weapon_knife_ursus" => "熊刀",
            "weapon_knife_navaja" => "纳瓦霍刀",
            "weapon_knife_stiletto" => "细剑",
            "weapon_knife_talon" => "利爪刀",
            "weapon_knife_cleaver" => "切肉刀",
            "weapon_knife_skeleton" => "骨刀",
            "weapon_knife_survival" => "求生刀",
            "weapon_knife_nomad" => "游牧刀",
            "weapon_knife_paracord" => "伞绳刀",
            "weapon_knife_classic" => "经典刀",
            _ => weaponName.Replace("weapon_knife_", "").Replace("weapon_", "")
        };
    }
}
