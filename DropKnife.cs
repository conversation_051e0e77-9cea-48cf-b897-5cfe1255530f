using CounterStrikeSharp.API;
using CounterStrikeSharp.API.Core;
using CounterStrikeSharp.API.Core.Attributes;
using CounterStrikeSharp.API.Core.Attributes.Registration;
using CounterStrikeSharp.API.Modules.Commands;
using CounterStrikeSharp.API.Modules.Events;
using CounterStrikeSharp.API.Modules.Utils;
using System.Text.Json;

namespace DropKnife;

[MinimumApiVersion(210)]
public class DropKnifePlugin : BasePlugin
{
    public override string ModuleName => "DropKnife";
    public override string ModuleVersion => "1.3.0";
    public override string ModuleAuthor => "AwakenDream";
    public override string ModuleDescription => "Drop knives at player's feet when typing .drop";

    private DropKnifeConfig _config = new();
    private readonly Dictionary<ulong, DateTime> _lastUsed = new();
    private static List<int> dropedPlayerSlots = new();

    public override void Load(bool hotReload)
    {
        LoadConfig();
        
        // 注册控制台命令
        AddCommand("css_drop", "Drop knives at your feet", OnDropCommand);
        AddCommand("drop", "Drop knives at your feet", OnDropCommand);
        AddCommand("css_dropknife_mode", "Toggle between cooldown and once-per-round mode", OnModeCommand);

        Console.WriteLine($"[DropKnife] Plugin loaded! Version: {ModuleVersion}");
    }

    [GameEventHandler]
    public HookResult OnRoundStart(EventRoundStart @event, GameEventInfo info)
    {
        dropedPlayerSlots.Clear();
        return HookResult.Continue;
    }

    [GameEventHandler]
    public HookResult OnPlayerChat(EventPlayerChat @event, GameEventInfo info)
    {
        // 确保只处理聊天消息，不处理其他类型的消息
        if (@event == null || string.IsNullOrEmpty(@event.Text))
            return HookResult.Continue;

        string message = @event.Text.Trim().ToLower();

        // 严格匹配聊天命令，防止意外触发
        if (message == "!drop" || message == ".drop" || message == ".d" ||
            message == "/drop" || message == "!d" || message == "/d")
        {
            int playerSlot = @event.Userid;
            try
            {
                CCSPlayerController player = Utilities.GetPlayerFromSlot(playerSlot)!;
                if (player == null || !player.IsValid || player.IsBot || player.IsHLTV)
                {
                    return HookResult.Continue;
                }

                // 确保玩家存活且在游戏中
                if (!player.PawnIsAlive)
                {
                    player.PrintToChat($" {ChatColors.Red}[DropKnife] 你必须存活才能使用此命令!");
                    return HookResult.Continue;
                }

                DoDropKnife(player);
            }
            catch (System.Exception ex)
            {
                Console.WriteLine($"[DropKnife] Error in chat handler: {ex.Message}");
                return HookResult.Continue;
            }
        }
        return HookResult.Continue;
    }

    public void OnDropCommand(CCSPlayerController? player, CommandInfo command)
    {
        if (player == null || !player.IsValid || player.IsBot)
            return;

        // 确保玩家存活
        if (!player.PawnIsAlive)
        {
            player.PrintToChat($" {ChatColors.Red}[DropKnife] 你必须存活才能使用此命令!");
            return;
        }

        DoDropKnife(player);
    }

    public void OnModeCommand(CCSPlayerController? player, CommandInfo command)
    {
        if (player == null || !player.IsValid)
            return;

        // 切换模式
        _config.UseCooldownMode = !_config.UseCooldownMode;
        SaveConfig();

        string modeText = _config.UseCooldownMode ? "冷却模式" : "每回合一次模式";
        player.PrintToChat($" {ChatColors.Green}[DropKnife] 已切换到 {modeText}");

        // 清除相关记录
        if (!_config.UseCooldownMode)
        {
            _lastUsed.Clear(); // 切换到每回合一次模式时清除冷却记录
        }
        else
        {
            dropedPlayerSlots.Clear(); // 切换到冷却模式时清除回合记录
        }
    }

    public void DoDropKnife(CCSPlayerController sender)
    {
        // 检查模式：冷却模式 vs 每回合一次模式
        if (_config.UseCooldownMode)
        {
            // 冷却模式：检查冷却时间
            if (_lastUsed.ContainsKey(sender.SteamID))
            {
                var timeSinceLastUse = DateTime.Now - _lastUsed[sender.SteamID];
                if (timeSinceLastUse.TotalSeconds < _config.CommandCooldown)
                {
                    var remainingTime = _config.CommandCooldown - (int)timeSinceLastUse.TotalSeconds;
                    sender.PrintToChat($" {ChatColors.Red}[DropKnife] 请等待 {remainingTime} 秒后再使用!");
                    return;
                }
            }
        }
        else
        {
            // 每回合一次模式：检查是否已经使用过
            if (_config.OncePerRound && dropedPlayerSlots.Contains((int)sender.UserId!))
            {
                sender.PrintToChat($" {ChatColors.Red}[DropKnife] 每回合只能使用一次!");
                return;
            }
        }

        // 检查队伍限制
        if (!_config.AllowedTeams.Contains((int)sender.Team))
        {
            sender.PrintToChat($" {ChatColors.Red}[DropKnife] 你的队伍不允许使用此命令!");
            return;
        }

        // 检查最少玩家数量
        int playerCount = Utilities.GetPlayers().Count(p => p.IsValid && !p.IsBot);
        if (playerCount < _config.MinimumPlayers)
        {
            sender.PrintToChat($" {ChatColors.Red}[DropKnife] 需要至少 {_config.MinimumPlayers} 名玩家才能使用!");
            return;
        }

        // 记录使用
        if (_config.UseCooldownMode)
        {
            _lastUsed[sender.SteamID] = DateTime.Now;
        }
        else if (_config.OncePerRound)
        {
            dropedPlayerSlots.Add((int)sender.UserId!);
        }

        // 在发送者脚下生成匕首
        for (int i = 0; i < _config.KnifeCount; i++)
        {
            // 使用GiveNamedItem方法生成匕首
            nint knife_pointer = sender.GiveNamedItem("weapon_knife");
            CBasePlayerWeapon knife = new(knife_pointer);

            // 获取玩家当前位置
            var playerPosition = sender.PlayerPawn.Value!.AbsOrigin;
            if (playerPosition == null) continue;

            // 计算每把匕首的位置，形成一个小圆圈
            float angle = (float)(i * 2 * Math.PI / _config.KnifeCount);

            // 设置新位置
            var newPosition = new Vector(
                playerPosition.X + (float)(Math.Cos(angle) * _config.SpawnRadius),
                playerPosition.Y + (float)(Math.Sin(angle) * _config.SpawnRadius),
                playerPosition.Z + _config.SpawnHeight
            );
            knife.Teleport(newPosition);
        }

        sender.PrintToChat($" {ChatColors.Green}[DropKnife] 已为你的队伍生成匕首!");
        
        // 向所有玩家显示消息（如果配置允许）
        if (_config.ShowPublicMessage)
        {
            Server.PrintToChatAll($" {ChatColors.LightBlue}[DropKnife] {ChatColors.White}{sender.PlayerName} {ChatColors.LightBlue}使用了 .drop 命令!");
        }
    }

    private void LoadConfig()
    {
        string configPath = Path.Combine(ModuleDirectory, "config.json");
        
        if (File.Exists(configPath))
        {
            try
            {
                string json = File.ReadAllText(configPath);
                _config = JsonSerializer.Deserialize<DropKnifeConfig>(json) ?? new DropKnifeConfig();
                Console.WriteLine("[DropKnife] Configuration loaded successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DropKnife] Error loading config: {ex.Message}");
                _config = new DropKnifeConfig();
            }
        }
        else
        {
            _config = new DropKnifeConfig();
            SaveConfig();
            Console.WriteLine("[DropKnife] Default configuration created!");
        }
    }

    private void SaveConfig()
    {
        try
        {
            string configPath = Path.Combine(ModuleDirectory, "config.json");
            string json = JsonSerializer.Serialize(_config, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(configPath, json);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[DropKnife] Error saving config: {ex.Message}");
        }
    }
}


