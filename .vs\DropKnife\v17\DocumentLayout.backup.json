{"Version": 1, "WorkspaceRootPath": "E:\\.CS2 plugins\\DropKnife\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A1DA1F86-F19E-DBA1-6DCD-31BF29C3BCE7}|DropKnife.csproj|e:\\.cs2 plugins\\dropknife\\dropknife.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1DA1F86-F19E-DBA1-6DCD-31BF29C3BCE7}|DropKnife.csproj|solutionrelative:dropknife.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "DropKnife.cs", "DocumentMoniker": "E:\\.CS2 plugins\\DropKnife\\DropKnife.cs", "RelativeDocumentMoniker": "DropKnife.cs", "ToolTip": "E:\\.CS2 plugins\\DropKnife\\DropKnife.cs", "RelativeToolTip": "DropKnife.cs", "ViewState": "AgIAABUAAAAAAAAAAAAuwHIAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T09:52:37.891Z", "EditorCaption": ""}]}]}]}