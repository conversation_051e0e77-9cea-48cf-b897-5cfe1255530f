using System.Text.Json.Serialization;

namespace DropKnife;

public class DropKnifeConfig
{
    [JsonPropertyName("knife_count")]
    public int KnifeCount { get; set; } = 5;
    
    [JsonPropertyName("spawn_radius")]
    public float SpawnRadius { get; set; } = 50.0f;
    
    [JsonPropertyName("spawn_height")]
    public float SpawnHeight { get; set; } = 10.0f;
    
    [JsonPropertyName("command_cooldown")]
    public int CommandCooldown { get; set; } = 5; // 秒

    [JsonPropertyName("use_cooldown_mode")]
    public bool UseCooldownMode { get; set; } = false; // true=冷却模式, false=每回合一次模式

    [JsonPropertyName("once_per_round")]
    public bool OncePerRound { get; set; } = true; // 每回合只能使用一次

    [JsonPropertyName("show_public_message")]
    public bool ShowPublicMessage { get; set; } = true;

    [JsonPropertyName("allowed_teams")]
    public List<int> AllowedTeams { get; set; } = new() { 2, 3 }; // 2=T, 3=CT

    [JsonPropertyName("minimum_players")]
    public int MinimumPlayers { get; set; } = 1;
}
